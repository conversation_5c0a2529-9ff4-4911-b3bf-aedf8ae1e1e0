import React, { ReactNode, useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import MedicalIllustration from '../components/MedicalIllustration';
import Logo from '../components/Logo';

interface AuthLayoutProps {
  children: ReactNode;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const bgRef = useRef<HTMLDivElement>(null);
  const logoRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    // Animate background elements
    gsap.fromTo(
      bgRef.current,
      { opacity: 0 },
      { opacity: 1, duration: 1, ease: 'power2.out' }
    );
    
    gsap.fromTo(
      logoRef.current,
      { opacity: 0, y: -20 },
      { opacity: 1, y: 0, duration: 0.8, ease: 'back.out(1.7)' }
    );
  }, []);

  return (
    <div className="min-h-screen flex flex-col md:flex-row">
      {/* Main content - Form side with logo */}
      <div className="w-full md:w-1/2 flex flex-col bg-white">
        <div ref={logoRef} className="pt-6 pb-2">
          <Logo />
        </div>

        <div className="flex-grow flex items-center justify-center py-8 px-4 md:px-8">
          <div className="w-full max-w-md mx-auto bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
            {children}
          </div>
        </div>

        <footer className="py-4 text-center text-sm text-slate-500">
          &copy; {new Date().getFullYear()} MedConnect AI. All rights reserved.
        </footer>
      </div>

      {/* Illustration side with full-screen DNA video background */}
      <div
        ref={bgRef}
        className="hidden md:block w-full md:w-1/2 relative overflow-hidden auth-video-container"
        style={{ height: '100vh' }}
      >
            {/* DNA Video Background */}
            <div className="absolute inset-0 w-full h-full">
              <video
                autoPlay
                muted
                loop
                playsInline
                preload="auto"
                className="absolute inset-0 w-full h-full object-cover auth-video-bg"
                style={{
                  zIndex: 1,
                }}
              >
                <source src="/DNA.mp4" type="video/mp4" />
                {/* Fallback for browsers that don't support video */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary to-secondary"></div>
              </video>
              {/* Video overlay for better text readability and branding */}
              <div className="absolute inset-0 bg-gradient-to-br from-primary/75 via-purple-600/60 to-secondary/75" style={{ zIndex: 2 }}></div>
              {/* Additional subtle pattern overlay */}
              <div className="absolute inset-0 opacity-10" style={{
                zIndex: 2,
                backgroundImage: 'radial-gradient(circle at 25% 25%, white 2px, transparent 2px)',
                backgroundSize: '50px 50px'
              }}></div>
            </div>

            {/* Content overlay - No card container, just text over video */}
            <div className="relative p-8 h-full flex flex-col justify-center items-center text-white" style={{ zIndex: 3 }}>
              <div className="text-center mb-8">
                <MedicalIllustration />
              </div>
              <h2 className="text-3xl font-bold text-center drop-shadow-lg mb-4">
                Advanced Healthcare Solutions
              </h2>
              <p className="text-center text-white/95 max-w-md drop-shadow-md leading-relaxed text-lg">
                Connect with healthcare professionals and get AI-powered medical insights all in one platform.
              </p>
            </div>
          </div>
      </div>
    </div>
  );
};

export default AuthLayout;